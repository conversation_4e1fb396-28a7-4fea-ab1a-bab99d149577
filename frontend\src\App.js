import React, { Suspense, useEffect, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import ErrorBoundary from './components/ErrorBoundary';
import Loading from './components/Loading';
import Navbar from './components/Navbar';
import env from './env';
import 'react-toastify/dist/ReactToastify.css';
import './App.css';

// Lazy load components for better performance
const Dashboard = lazy(() => import('./components/Dashboard'));
const ChannelDetail = lazy(() => import('./components/ChannelDetail'));
const AddChannel = lazy(() => import('./components/AddChannel'));

/**
 * Main App Component
 * Provides routing, error boundaries, and global state management
 */
const App = () => {
  // Log environment information for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 Running in ${env.REACT_APP_ENV} environment`);
      console.log(`🔗 API URL: ${env.REACT_APP_API_URL}`);
      console.log('🔧 Environment config:', env);
      console.log('🌐 Window._env_:', window._env_);
    }
  }, []);

  return (
    <ErrorBoundary>
      <Router>
        <div className="app-container">
          <Navbar />
          <main className="content-container">
            <Suspense fallback={<Loading fullScreen message="Loading application..." />}>
              <Routes>
                <Route
                  path="/"
                  element={<Dashboard />}
                />
                <Route
                  path="/channels/:channelId"
                  element={<ChannelDetail />}
                />
                <Route
                  path="/add-channel"
                  element={<AddChannel />}
                />
                {/* 404 Route */}
                <Route
                  path="*"
                  element={
                    <div className="not-found">
                      <h1>404 - Page Not Found</h1>
                      <p>The page you're looking for doesn't exist.</p>
                    </div>
                  }
                />
              </Routes>
            </Suspense>
          </main>

          {/* Toast notifications with modern configuration */}
          <ToastContainer
            position="bottom-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
            toastClassName="custom-toast"
            bodyClassName="custom-toast-body"
            progressClassName="custom-toast-progress"
          />
        </div>
      </Router>
    </ErrorBoundary>
  );
};

export default App;
