name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.12'

jobs:
  # Frontend Tests and Build
  frontend:
    name: Frontend CI
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
      
    - name: Run ESLint
      working-directory: ./frontend
      run: npm run lint
      
    - name: Check Prettier formatting
      working-directory: ./frontend
      run: npm run format:check
      
    - name: Run tests
      working-directory: ./frontend
      run: npm test -- --coverage --watchAll=false
      
    - name: Build application
      working-directory: ./frontend
      run: npm run build
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: frontend-build
        path: frontend/build/
        retention-days: 7

  # Backend Tests and Build
  backend:
    name: Backend CI
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16-alpine
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_youtube_stats
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
        
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Run linting
      working-directory: ./backend
      run: |
        pip install black isort flake8
        black --check .
        isort --check-only .
        flake8 .
        
    - name: Run tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_youtube_stats
        YOUTUBE_API_KEY: test_key
      run: pytest --cov=. --cov-report=xml
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

  # Docker Build Test
  docker:
    name: Docker Build Test
    runs-on: ubuntu-latest
    needs: [frontend, backend]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        file: ./frontend/Dockerfile
        push: false
        tags: youtube-stats-frontend:test
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Build backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        file: ./backend/Dockerfile
        push: false
        tags: youtube-stats-backend:test
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Test docker-compose
      run: |
        cp .env.example .env
        echo "YOUTUBE_API_KEY=test_key" >> .env
        docker-compose config

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Deployment (only on main branch)
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [frontend, backend, docker, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to production
      run: |
        echo "🚀 Deploying to production..."
        # Add your deployment steps here
        # This could include:
        # - Building and pushing Docker images to registry
        # - Deploying to cloud provider (AWS, GCP, Azure)
        # - Updating Kubernetes manifests
        # - Running database migrations
        echo "✅ Deployment completed successfully!"
