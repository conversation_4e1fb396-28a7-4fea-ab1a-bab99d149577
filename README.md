# 🎥 YouTube Stats Tracker v2.0

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![React](https://img.shields.io/badge/React-18.3.1-blue.svg)](https://reactjs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115.4-green.svg)](https://fastapi.tiangolo.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-16-blue.svg)](https://www.postgresql.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

A modern, responsive web application for tracking YouTube channel statistics and analytics with real-time updates and beautiful asymmetrical dashboard layout.

## ✨ Features

### 📊 Analytics & Tracking
- **Multi-channel tracking** - Monitor unlimited YouTube channels
- **Real-time statistics** - Live subscriber, view, and video counts
- **Historical data** - Track growth over time
- **Smart sorting** - Sort by subscribers, views, videos, or last updated
- **Responsive design** - Perfect on desktop, tablet, and mobile

### 🎨 Modern UI/UX
- **Asymmetrical grid layout** - Visually appealing non-uniform card sizes
- **Smooth animations** - Polished transitions and hover effects
- **Dark/light themes** - Adaptive color schemes
- **Accessibility** - WCAG compliant interface
- **Progressive Web App** - Install as native app

### 🔧 Technical Excellence
- **Modern React patterns** - Hooks, functional components, custom hooks
- **TypeScript ready** - Full type safety support
- **Performance optimized** - Lazy loading, memoization, code splitting
- **SEO friendly** - Server-side rendering ready
- **Docker containerized** - Easy deployment and scaling

## 🚀 Tech Stack

### Frontend
- **React 18.3.1** - Latest React with concurrent features
- **React Router 6.26.2** - Modern routing
- **React Icons 5.3.0** - Comprehensive icon library
- **Chart.js 4.4.4** - Beautiful charts and graphs
- **Axios 1.7.7** - HTTP client with interceptors
- **React Toastify 10.0.5** - Elegant notifications

### Backend
- **FastAPI 0.115.4** - High-performance async API framework
- **SQLAlchemy 2.0.36** - Modern ORM with async support
- **Pydantic 2.9.2** - Data validation and serialization
- **PostgreSQL 16** - Robust relational database
- **Uvicorn 0.32.0** - Lightning-fast ASGI server

### DevOps & Tools
- **Docker & Docker Compose** - Containerization
- **ESLint 8.57.0** - Code linting
- **Prettier 3.3.3** - Code formatting
- **Nginx 1.25** - Reverse proxy and static serving
- **GitHub Actions** - CI/CD pipeline

## 📋 Prerequisites

- **Docker** 20.10+ and **Docker Compose** 2.0+
- **YouTube Data API v3 key** ([Get one here](https://console.developers.google.com/))
- **Node.js** 20+ (for local development)
- **Python** 3.12+ (for local development)

## 🛠️ Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/youtube-stats-tracker.git
cd youtube-stats-tracker
```

### 2. Environment Configuration
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your configuration
nano .env
```

**Required Environment Variables:**
```env
# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=youtube_stats
DATABASE_URL=********************************************************/youtube_stats

# Application Configuration
REACT_APP_API_URL=http://localhost:8000
FRONTEND_PORT=3000
BACKEND_PORT=8000
```

### 3. Get YouTube API Key
1. Visit [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable **YouTube Data API v3**
4. Create credentials (API Key)
5. Add the key to your `.env` file

### 4. Launch Application
```bash
# Build and start all services
docker-compose up --build

# Or run in background
docker-compose up -d --build
```

### 5. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## Usage

1. **Add a YouTube Channel**: Click "Add Channel" and enter a YouTube channel ID
2. **View Statistics**: Browse the dashboard to see channel statistics
3. **View History**: Click on any channel to see historical data and trends
4. **Automatic Updates**: The system automatically updates channel statistics every 6 hours

## API Endpoints

- `GET /channels/` - List all tracked channels
- `POST /channels/` - Add a new channel to track
- `GET /channels/{channel_id}` - Get specific channel details
- `GET /channels/{channel_id}/history` - Get channel historical data
- `DELETE /channels/{channel_id}` - Remove a channel from tracking

## Development

To run the application in development mode:

```bash
# Backend only
cd backend
pip install -r requirements.txt
uvicorn app:app --reload

# Frontend only
cd frontend
npm install
npm start
```

## Troubleshooting

- **Database Connection Issues**: Ensure PostgreSQL is running and environment variables are correct
- **YouTube API Errors**: Check that your API key is valid and has YouTube Data API v3 enabled
- **Port Conflicts**: Make sure ports 3000, 8000, and 5432 are available
