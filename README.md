# YouTube Statistics Tracker

A full-stack application for tracking and visualizing YouTube channel statistics over time. This application uses Python with FastAPI for the backend, React for the frontend, and PostgreSQL for the database.

## Features

- Track multiple YouTube channels in one dashboard
- View key statistics like subscribers, videos, and views
- Historical data tracking with visualizations
- Automatic updates of channel statistics
- Clean, responsive UI

## Technology Stack

- **Backend**: Python, FastAPI, SQLAlchemy
- **Frontend**: React, Chart.js, React Router
- **Database**: PostgreSQL
- **Deployment**: Docker, Docker Compose

## Prerequisites

- Docker and Docker Compose
- YouTube Data API key

## Setup and Installation

### 1. Clone the repository

```bash
git clone https://github.com/gauravksahni/youtube-stats-tracker.git
cd youtube-stats-tracker
```

### 2. Set up your YouTube API key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project
3. Enable the YouTube Data API v3
4. Create an API key
5. Restrict the API key to YouTube Data API v3 only

### 3. Configure environment variables

Create a `.env` file in the root directory with the following variables:

```bash
# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=youtube_stats
DATABASE_URL=********************************************/youtube_stats

# Network Configuration
IP_ADDR=http://localhost
```

### 4. Start the application with Docker Compose

```bash
docker-compose up -d
```

### 5. Access the application

- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Usage

1. **Add a YouTube Channel**: Click "Add Channel" and enter a YouTube channel ID
2. **View Statistics**: Browse the dashboard to see channel statistics
3. **View History**: Click on any channel to see historical data and trends
4. **Automatic Updates**: The system automatically updates channel statistics every 6 hours

## API Endpoints

- `GET /channels/` - List all tracked channels
- `POST /channels/` - Add a new channel to track
- `GET /channels/{channel_id}` - Get specific channel details
- `GET /channels/{channel_id}/history` - Get channel historical data
- `DELETE /channels/{channel_id}` - Remove a channel from tracking

## Development

To run the application in development mode:

```bash
# Backend only
cd backend
pip install -r requirements.txt
uvicorn app:app --reload

# Frontend only
cd frontend
npm install
npm start
```

## Troubleshooting

- **Database Connection Issues**: Ensure PostgreSQL is running and environment variables are correct
- **YouTube API Errors**: Check that your API key is valid and has YouTube Data API v3 enabled
- **Port Conflicts**: Make sure ports 3000, 8000, and 5432 are available
