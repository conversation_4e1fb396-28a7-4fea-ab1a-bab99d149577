version: '3.8'

services:
  postgres:
    image: postgres:16-alpine
    container_name: youtube-stats-postgres
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-youtube_stats}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-youtube_stats}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - youtube-stats-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: youtube-stats-backend
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL:-********************************************/youtube_stats}
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - ./backend:/app:ro
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - youtube-stats-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: youtube-stats-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000}
      - NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx/conf.d
    volumes:
      - ./frontend/nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - youtube-stats-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  youtube-stats-network:
    driver: bridge
    name: youtube-stats-network
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
    name: youtube-stats-postgres-data
