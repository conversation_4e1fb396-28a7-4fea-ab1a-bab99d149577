version: '3.8'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_USER=${POSTGRES_USER}
      POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - youtube-stats-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - youtube-stats-network
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"  
    environment:
      - REACT_APP_API_URL=${IP_ADDR}:8000
    volumes:
      - ./frontend/nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - backend
    networks:
      - youtube-stats-network
    restart: unless-stopped

networks:
  youtube-stats-network:
    driver: bridge

volumes:
  postgres_data:
