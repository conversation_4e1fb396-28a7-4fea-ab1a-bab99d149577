{"name": "youtube-stats-frontend", "version": "2.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "axios": "^1.7.7", "chart.js": "^4.4.4", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "react-toastify": "^10.0.5", "web-vitals": "^4.2.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "eslint": "^8.57.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "prettier": "^3.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}", "format:check": "prettier --check src/**/*.{js,jsx,ts,tsx,css,md}", "analyze": "npm run build && npx serve -s build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"react-hooks/exhaustive-deps": "warn", "no-unused-vars": "warn", "no-console": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false}, "proxy": "http://localhost:8000"}