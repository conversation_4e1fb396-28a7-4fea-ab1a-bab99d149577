/* VELIO Sidebar Design */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 240px;
  height: 100vh;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

/* Sidebar Header */
.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
}

.hamburger-icon {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
}

/* VELIO Logo */
.sidebar-logo {
  padding: var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
}

.logo-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.logo-icon {
  color: var(--accent-primary);
  font-size: var(--font-size-lg);
  margin-right: var(--spacing-sm);
}

.logo-text {
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.logo-subtitle {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-left: var(--spacing-lg);
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  list-style: none;
  padding: var(--spacing-lg) 0;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.nav-link:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.nav-item.active .nav-link {
  color: var(--accent-primary);
  background-color: var(--hover-bg);
  border-left-color: var(--accent-primary);
}

.nav-icon {
  font-size: var(--font-size-base);
  margin-right: var(--spacing-md);
  width: 16px;
  text-align: center;
}

.nav-text {
  font-size: var(--font-size-sm);
  font-weight: 400;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .sidebar-header {
    display: block;
  }
}

@media (min-width: 769px) {
  .sidebar-header {
    display: none;
  }
}
