.error-boundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.error-boundary-content {
  max-width: 600px;
  width: 100%;
  background: var(--color-background);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: 0 20px 40px var(--color-shadow);
  text-align: center;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-icon {
  font-size: 4rem;
  color: #ff6b6b;
  margin-bottom: var(--spacing-lg);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.error-boundary h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
}

.error-message {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.retry-button,
.reload-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
}

.retry-button {
  background: var(--color-primary);
  color: white;
}

.retry-button:hover {
  background: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 0, 0, 0.3);
}

.reload-button {
  background: var(--color-text-secondary);
  color: white;
}

.reload-button:hover {
  background: var(--color-text-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--color-shadow-hover);
}

.error-details {
  margin-top: var(--spacing-xl);
  text-align: left;
  background: #f8f9fa;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  border: 1px solid var(--color-border);
}

.error-details summary {
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  user-select: none;
}

.error-details summary:hover {
  color: var(--color-primary);
}

.error-stack {
  margin-top: var(--spacing-md);
}

.error-stack h3 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: var(--spacing-md) 0 var(--spacing-sm) 0;
}

.error-stack pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-help {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.error-help p {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin: 0;
}

.error-help a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.error-help a:hover {
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
  .error-boundary {
    padding: var(--spacing-lg);
  }
  
  .error-boundary-content {
    padding: var(--spacing-xl);
  }
  
  .error-icon {
    font-size: 3rem;
  }
  
  .error-boundary h1 {
    font-size: var(--font-size-xl);
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .retry-button,
  .reload-button {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
}
