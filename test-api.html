<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>YouTube Stats API Test</h1>
    <button onclick="testAPI()">Test API Connection</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing API...';
            
            try {
                console.log('Making API request to http://localhost:8000/channels/');
                const response = await fetch('http://localhost:8000/channels/?sort_by=subscriber_count');
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `
                    <h2>API Test Successful!</h2>
                    <p>Status: ${response.status}</p>
                    <p>Channels found: ${data.length}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('API test failed:', error);
                resultDiv.innerHTML = `
                    <h2>API Test Failed</h2>
                    <p>Error: ${error.message}</p>
                    <p>Check browser console for more details</p>
                `;
            }
        }
    </script>
</body>
</html>
