import React from 'react';
import { FaExclamationTriangle, FaRefresh } from 'react-icons/fa';
import './ErrorBoundary.css';

/**
 * Error Boundary Component
 * Catches JavaScript errors anywhere in the child component tree
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });

    // You can also log the error to an error reporting service here
    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: error.toString(),
        fatal: false,
      });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo } = this.state;
      const isDevelopment = process.env.NODE_ENV === 'development';

      return (
        <div className="error-boundary">
          <div className="error-boundary-content">
            <div className="error-icon">
              <FaExclamationTriangle />
            </div>
            
            <h1>Oops! Something went wrong</h1>
            
            <p className="error-message">
              We're sorry, but something unexpected happened. Please try refreshing the page.
            </p>

            <div className="error-actions">
              <button 
                onClick={this.handleRetry}
                className="retry-button"
                type="button"
              >
                <FaRefresh />
                Try Again
              </button>
              
              <button 
                onClick={this.handleReload}
                className="reload-button"
                type="button"
              >
                Reload Page
              </button>
            </div>

            {isDevelopment && error && (
              <details className="error-details">
                <summary>Error Details (Development Only)</summary>
                <div className="error-stack">
                  <h3>Error:</h3>
                  <pre>{error.toString()}</pre>
                  
                  {errorInfo && (
                    <>
                      <h3>Component Stack:</h3>
                      <pre>{errorInfo.componentStack}</pre>
                    </>
                  )}
                </div>
              </details>
            )}

            <div className="error-help">
              <p>
                If this problem persists, please{' '}
                <a 
                  href="https://github.com/yourusername/youtube-stats-tracker/issues"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  report an issue
                </a>
                {' '}on GitHub.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
