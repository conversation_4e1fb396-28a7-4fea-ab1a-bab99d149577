import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

/**
 * Custom hook for managing channel data and operations
 * Provides centralized state management for channels
 */
export const useChannels = () => {
  const [channels, setChannels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Get API base URL from environment
  const getApiBaseUrl = useCallback(() => {
    if (window._env_?.REACT_APP_API_URL) {
      return window._env_.REACT_APP_API_URL;
    }
    return process.env.REACT_APP_API_URL || 'http://localhost:8000';
  }, []);

  // Fetch channels from API
  const fetchChannels = useCallback(async (sortBy = 'subscriber_count') => {
    try {
      setLoading(true);
      setError(null);
      
      const apiBaseUrl = getApiBaseUrl();
      const response = await axios.get(`${apiBaseUrl}/channels/`, {
        params: { sort_by: sortBy },
        timeout: 10000,
      });
      
      setChannels(response.data);
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch channels';
      setError(errorMessage);
      toast.error(`Error loading channels: ${errorMessage}`);
      console.error('Error fetching channels:', err);
    } finally {
      setLoading(false);
    }
  }, [getApiBaseUrl]);

  // Add a new channel
  const addChannel = useCallback(async (channelUrl) => {
    try {
      setLoading(true);
      const apiBaseUrl = getApiBaseUrl();
      
      await axios.post(`${apiBaseUrl}/channels/`, {
        channel_url: channelUrl,
      });
      
      toast.success('Channel added successfully!');
      await fetchChannels(); // Refresh the list
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to add channel';
      setError(errorMessage);
      toast.error(`Error adding channel: ${errorMessage}`);
      console.error('Error adding channel:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [getApiBaseUrl, fetchChannels]);

  // Delete a channel
  const deleteChannel = useCallback(async (channelId) => {
    try {
      const apiBaseUrl = getApiBaseUrl();
      
      await axios.delete(`${apiBaseUrl}/channels/${channelId}`);
      
      toast.success('Channel deleted successfully!');
      await fetchChannels(); // Refresh the list
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to delete channel';
      setError(errorMessage);
      toast.error(`Error deleting channel: ${errorMessage}`);
      console.error('Error deleting channel:', err);
      return false;
    }
  }, [getApiBaseUrl, fetchChannels]);

  // Initial load
  useEffect(() => {
    fetchChannels();
  }, [fetchChannels]);

  return {
    channels,
    loading,
    error,
    fetchChannels,
    addChannel,
    deleteChannel,
    refetch: fetchChannels,
  };
};

export default useChannels;
