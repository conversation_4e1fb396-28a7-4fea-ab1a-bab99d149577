import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaPlay, FaList, FaBookmark, FaCog, FaBars } from 'react-icons/fa';
import './Navbar.css';

const Navbar = () => {
  const location = useLocation();

  return (
    <nav className="sidebar">
      {/* Hamburger menu for mobile */}
      <div className="sidebar-header">
        <FaBars className="hamburger-icon" />
      </div>

      {/* VELIO Logo */}
      <div className="sidebar-logo">
        <div className="logo-container">
          <FaPlay className="logo-icon" />
          <span className="logo-text">VELIO</span>
        </div>
        <span className="logo-subtitle">BETA</span>
      </div>

      {/* Navigation Links */}
      <ul className="sidebar-nav">
        <li className={location.pathname === '/' ? 'nav-item active' : 'nav-item'}>
          <Link to="/" className="nav-link">
            <FaList className="nav-icon" />
            <span className="nav-text">Tracked channels</span>
          </Link>
        </li>
        <li className={location.pathname === '/add-channel' ? 'nav-item active' : 'nav-item'}>
          <Link to="/add-channel" className="nav-link">
            <FaBookmark className="nav-icon" />
            <span className="nav-text">Saved videos</span>
          </Link>
        </li>
        <li className="nav-item">
          <Link to="/settings" className="nav-link">
            <FaCog className="nav-icon" />
            <span className="nav-text">Setting</span>
          </Link>
        </li>
      </ul>
    </nav>
  );
};

export default Navbar;
