# Environment variables
.env
.env.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# React / Node.js
node_modules/
/frontend/build
/frontend/.env
/frontend/.env.*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp/
.pnp.js
coverage/

# Docker
.docker/

# Database
postgres_data/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
