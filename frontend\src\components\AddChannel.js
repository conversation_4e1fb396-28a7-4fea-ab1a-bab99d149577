import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaYoutube, FaArrowLeft, FaPlus } from 'react-icons/fa';
import { useChannels } from '../hooks/useChannels';
import { ButtonLoading } from './Loading';
import { REGEX } from '../constants';
import './AddChannel.css';

const AddChannel = () => {
  const navigate = useNavigate();
  const { addChannel, loading } = useChannels();
  const [channelUrl, setChannelUrl] = useState('');
  const [error, setError] = useState('');
  const [validationError, setValidationError] = useState('');

  // Validate YouTube URL format
  const validateUrl = useCallback((url) => {
    if (!url.trim()) {
      return 'Please enter a YouTube channel URL';
    }

    if (!REGEX.YOUTUBE_URL.test(url)) {
      return 'Please enter a valid YouTube channel URL';
    }

    return null;
  }, []);

  // Handle URL input change with validation
  const handleUrlChange = useCallback((e) => {
    const url = e.target.value;
    setChannelUrl(url);

    // Clear previous errors
    setError('');
    setValidationError('');

    // Validate URL format
    const urlError = validateUrl(url);
    if (urlError) {
      setValidationError(urlError);
    }
  }, [validateUrl]);

  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    // Validate URL
    const urlError = validateUrl(channelUrl);
    if (urlError) {
      setValidationError(urlError);
      return;
    }

    // Clear errors
    setError('');
    setValidationError('');

    try {
      const success = await addChannel(channelUrl.trim());
      if (success) {
        toast.success('Channel added successfully! 🎉');
        navigate('/');
      }
    } catch (err) {
      console.error('Error adding channel:', err);
      setError('Failed to add channel. Please try again.');
    }
  }, [channelUrl, validateUrl, addChannel, navigate]);

  // Handle back navigation
  const handleBack = useCallback(() => {
    navigate('/');
  }, [navigate]);

  return (
    <div className="add-channel-container">
      <div className="add-channel-card">
        <div className="add-channel-header">
          <button
            type="button"
            onClick={handleBack}
            className="back-button"
            aria-label="Go back to dashboard"
          >
            <FaArrowLeft />
          </button>
          <div className="header-content">
            <FaYoutube className="youtube-icon" />
            <h1>Add YouTube Channel</h1>
            <p>Start tracking a new YouTube channel's statistics</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="add-channel-form">
          <div className="form-group">
            <label htmlFor="channel-url">
              YouTube Channel URL <span className="required">*</span>
            </label>
            <input
              type="url"
              id="channel-url"
              value={channelUrl}
              onChange={handleUrlChange}
              placeholder="https://www.youtube.com/channel/UC... or https://www.youtube.com/@username"
              className={`form-control ${validationError ? 'error' : ''}`}
              required
              aria-describedby="url-help url-error"
            />
            <small id="url-help" className="form-text">
              Enter any YouTube channel URL format (channel/, c/, @username, or /user/)
            </small>
            {validationError && (
              <div id="url-error" className="validation-error" role="alert">
                {validationError}
              </div>
            )}
          </div>

          {error && (
            <div className="error-message" role="alert">
              <strong>Error:</strong> {error}
            </div>
          )}
          
          <div className="form-actions">
            <button
              type="button"
              className="cancel-btn"
              onClick={handleBack}
              disabled={loading}
            >
              Cancel
            </button>
            <ButtonLoading
              type="submit"
              className="submit-btn"
              loading={loading}
              disabled={loading || !!validationError}
            >
              <FaPlus />
              Add Channel
            </ButtonLoading>
          </div>
        </form>

        <div className="help-section">
          <h3>📋 Supported URL Formats</h3>
          <div className="url-examples">
            <div className="url-example">
              <strong>Channel URL:</strong>
              <code>https://www.youtube.com/channel/UC_x5XG1OV2P6uZZ5FSM9Ttw</code>
            </div>
            <div className="url-example">
              <strong>Custom URL:</strong>
              <code>https://www.youtube.com/c/channelname</code>
            </div>
            <div className="url-example">
              <strong>Handle URL:</strong>
              <code>https://www.youtube.com/@username</code>
            </div>
            <div className="url-example">
              <strong>User URL:</strong>
              <code>https://www.youtube.com/user/username</code>
            </div>
          </div>

          <div className="tips">
            <h4>💡 Tips</h4>
            <ul>
              <li>Copy the URL directly from your browser's address bar</li>
              <li>The system will automatically detect the channel from any valid YouTube URL</li>
              <li>Make sure the channel is public and accessible</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddChannel;
