// Environment configuration for the React frontend
// This file handles environment variables for both development and production

const env = {
  // Environment type
  REACT_APP_ENV: process.env.NODE_ENV || 'development',
  
  // API URL configuration
  REACT_APP_API_URL: (() => {
    // In development, use the proxy configured in package.json
    if (process.env.NODE_ENV === 'development') {
      return 'http://localhost:8000';
    }
    
    // In production, check for runtime environment variables first
    if (window._env_ && window._env_.REACT_APP_API_URL) {
      return window._env_.REACT_APP_API_URL;
    }
    
    // Fallback to build-time environment variable
    if (process.env.REACT_APP_API_URL) {
      return process.env.REACT_APP_API_URL;
    }
    
    // Default fallback for production
    return 'http://localhost:8000';
  })()
};

export default env;
