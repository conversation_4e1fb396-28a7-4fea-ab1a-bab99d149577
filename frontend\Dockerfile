# Stage 1: Build the React application
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app

# Add metadata
LABEL maintainer="YouTube Stats Tracker"
LABEL version="2.0.0"

# Add package.json and package-lock.json (if available)
COPY package.json package-lock.json* ./

# Use npm ci for faster, reliable, reproducible builds
RUN npm ci --only=production --silent

# Copy all files
COPY . .

# Build the application with optimizations
RUN npm run build

# Stage 2: Serve the application with Nginx
FROM nginx:1.25-alpine

# Install security updates and curl for health checks
RUN apk update && apk upgrade && apk add --no-cache \
    ca-certificates \
    curl \
    && rm -rf /var/cache/apk/*

# Copy the build output from the previous stage
COPY --from=build /app/build /usr/share/nginx/html

# Copy custom Nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Expose port 80
EXPOSE 80

# Use non-root user for security
USER nginx

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
