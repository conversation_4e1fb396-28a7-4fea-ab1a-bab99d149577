import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Line } from 'react-chartjs-2';
import { toast } from 'react-toastify';
import { FaYoutube, FaEye, FaVideo, FaUserFriends, FaTrash, FaArrowLeft, FaRedo } from 'react-icons/fa';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { channelApi } from '../services/api';
import { formatNumber, formatDate, getRelativeTime } from '../utils/formatters';
import Loading, { ButtonLoading } from './Loading';
import './ChannelDetail.css';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const ChannelDetail = () => {
  const { channelId } = useParams();
  const navigate = useNavigate();
  const [channel, setChannel] = useState(null);
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState(null);
  const [chartMetric, setChartMetric] = useState('subscriber_count');
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  // Load data on mount and when channelId changes
  useEffect(() => {
    fetchChannelDetails();
  }, [fetchChannelDetails]);

  // Fetch channel details and history
  const fetchChannelDetails = useCallback(async () => {
    if (!channelId) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch channel details and history in parallel
      const [channelResponse, historyResponse] = await Promise.all([
        channelApi.getById(channelId),
        channelApi.getHistory(channelId, 30)
      ]);

      setChannel(channelResponse.data);

      // Sort history by timestamp ascending for the chart
      const sortedHistory = [...historyResponse.data].sort(
        (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
      );
      setHistory(sortedHistory);

    } catch (err) {
      console.error('Error fetching channel details:', err);
      setError('Failed to load channel details. Please try again later.');
      toast.error('Failed to load channel details');
    } finally {
      setLoading(false);
    }
  }, [channelId]);

  // Refresh channel data
  const handleRefresh = useCallback(async () => {
    if (!channelId) return;

    try {
      setRefreshing(true);
      await channelApi.refresh(channelId);
      await fetchChannelDetails();
      toast.success('Channel data refreshed successfully! 🔄');
    } catch (err) {
      console.error('Error refreshing channel:', err);
      toast.error('Failed to refresh channel data');
    } finally {
      setRefreshing(false);
    }
  }, [channelId, fetchChannelDetails]);

  // Delete channel with confirmation
  const handleDeleteChannel = useCallback(async () => {
    if (!deleteConfirm) {
      setDeleteConfirm(true);
      return;
    }

    try {
      setDeleting(true);
      await channelApi.delete(channelId);
      toast.success('Channel deleted successfully! 🗑️');
      navigate('/');
    } catch (err) {
      console.error('Error deleting channel:', err);
      toast.error('Failed to delete channel. Please try again.');
    } finally {
      setDeleting(false);
      setDeleteConfirm(false);
    }
  }, [channelId, deleteConfirm, navigate]);

  // Handle back navigation
  const handleBack = useCallback(() => {
    navigate('/');
  }, [navigate]);

  // Cancel delete confirmation
  const cancelDelete = useCallback(() => {
    setDeleteConfirm(false);
  }, []);

  // Handle chart metric change
  const handleMetricChange = useCallback((e) => {
    setChartMetric(e.target.value);
  }, []);

  // Memoized chart data
  const chartData = useMemo(() => {
    if (!history.length) return null;

    const labels = history.map(entry =>
      formatDate(entry.timestamp)
    );

    const metricConfig = {
      subscriber_count: {
        label: 'Subscribers',
        borderColor: 'rgb(255, 0, 0)',
        backgroundColor: 'rgba(255, 0, 0, 0.1)',
      },
      view_count: {
        label: 'Views',
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.1)',
      },
      video_count: {
        label: 'Videos',
        borderColor: 'rgb(255, 205, 86)',
        backgroundColor: 'rgba(255, 205, 86, 0.1)',
      },
    };

    const config = metricConfig[chartMetric] || metricConfig.subscriber_count;

    return {
      labels,
      datasets: [
        {
          label: config.label,
          data: history.map(entry => entry[chartMetric]),
          borderColor: config.borderColor,
          backgroundColor: config.backgroundColor,
          tension: 0.4,
          fill: true,
          pointBackgroundColor: config.borderColor,
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6,
        },
      ],
    };
  }, [history, chartMetric]);

  // Memoized chart options
  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      title: {
        display: true,
        text: `${chartMetric === 'subscriber_count' ? 'Subscribers' :
               chartMetric === 'view_count' ? 'Views' : 'Videos'} Over Time`,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: 20,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: (context) => {
            const value = formatNumber(context.parsed.y);
            return `${context.dataset.label}: ${value}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      y: {
        beginAtZero: false,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          callback: (value) => formatNumber(value),
        },
      },
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
  }), [chartMetric]);

  // Loading state
  if (loading) {
    return <Loading fullScreen message="Loading channel details..." />;
  }

  // Error state
  if (error) {
    return (
      <div className="channel-detail-container">
        <div className="error-state">
          <h2>Failed to load channel details</h2>
          <p>{error}</p>
          <button onClick={fetchChannelDetails} className="retry-button">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Channel not found
  if (!channel) {
    return (
      <div className="channel-detail-container">
        <div className="not-found-state">
          <h2>Channel not found</h2>
          <p>The requested channel could not be found.</p>
          <button onClick={handleBack} className="back-button">
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="channel-detail-container">
      <div className="channel-header">
        <div className="channel-title">
          <FaYoutube className="youtube-icon" />
          <h1>{channel.title}</h1>
        </div>
        <div className="channel-actions">
          {deleteConfirm ? (
            <div className="delete-confirmation">
              <span>Are you sure?</span>
              <button className="confirm-delete-btn" onClick={handleDeleteChannel}>
                Yes, Delete
              </button>
              <button className="cancel-delete-btn" onClick={cancelDelete}>
                Cancel
              </button>
            </div>
          ) : (
            <button className="delete-channel-btn" onClick={handleDeleteChannel}>
              <FaTrash /> Delete Channel
            </button>
          )}
        </div>
      </div>

      <div className="channel-description">
        <p>{channel.description}</p>
        <div className="channel-metadata">
          <span>Channel ID: {channel.channel_id}</span>
          <span>Created: {new Date(channel.published_at).toLocaleDateString()}</span>
          <span>Last Updated: {new Date(channel.last_updated).toLocaleString()}</span>
        </div>
      </div>

      <div className="stats-container">
        <div className="stat-card">
          <FaUserFriends className="stat-icon subscribers" />
          <div className="stat-content">
            <h3>Subscribers</h3>
            <div className="stat-value">{formatNumber(channel.subscriber_count)}</div>
          </div>
        </div>
        <div className="stat-card">
          <FaVideo className="stat-icon videos" />
          <div className="stat-content">
            <h3>Videos</h3>
            <div className="stat-value">{formatNumber(channel.video_count)}</div>
          </div>
        </div>
        <div className="stat-card">
          <FaEye className="stat-icon views" />
          <div className="stat-content">
            <h3>Total Views</h3>
            <div className="stat-value">{formatNumber(channel.view_count)}</div>
          </div>
        </div>
      </div>

      <div className="history-section">
        <div className="history-header">
          <h2>Historical Data</h2>
          <div className="metric-selector">
            <label htmlFor="metric-select">Show metric:</label>
            <select id="metric-select" value={chartMetric} onChange={handleMetricChange}>
              <option value="subscriber_count">Subscribers</option>
              <option value="view_count">Views</option>
              <option value="video_count">Videos</option>
            </select>
          </div>
        </div>

        {history.length > 0 && chartData ? (
          <div className="chart-container">
            <Line data={chartData} options={chartOptions} />
          </div>
        ) : (
          <div className="no-history">
            <p>No historical data available yet. Data will be collected over time.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChannelDetail;
