.dashboard-container {
  padding: 20px 0;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 24px 0;
  border-bottom: 2px solid #f5f5f5;
  position: relative;
}

.dashboard-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #ff0000, #ff6b6b);
  border-radius: 2px;
}

.dashboard-header h1 {
  font-size: 32px;
  color: #333;
  font-weight: 800;
  letter-spacing: -0.5px;
  background: linear-gradient(135deg, #333, #666);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.controls {
  display: flex;
  align-items: center;
  gap: 24px;
}

.sort-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sort-control label {
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.sort-control select {
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.sort-control select:focus {
  outline: none;
  border-color: #ff0000;
  box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.1);
}

.sort-control select:hover {
  border-color: #ff6b6b;
}

.add-channel-btn {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #ff0000, #ff4444);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(255, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.add-channel-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.add-channel-btn:hover {
  background: linear-gradient(135deg, #d32f2f, #ff0000);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 0, 0, 0.3);
}

.add-channel-btn:hover::before {
  left: 100%;
}

.no-channels {
  background-color: #fff;
  padding: 40px;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-channels p {
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
}

/* Asymmetric Grid Layout */
.asymmetric-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-auto-rows: minmax(200px, auto);
  gap: 20px;
  padding: 20px 0;
}

/* Base Channel Card */
.channel-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  color: inherit;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out forwards;
  animation-delay: var(--animation-delay, 0s);
  opacity: 0;
  transform: translateY(30px);

  /* Default size: spans 4 columns, 1 row */
  grid-column: span 4;
  grid-row: span 1;
}

/* Card Size Variants */
.channel-card.large {
  grid-column: span 6;
  grid-row: span 2;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  color: white;
}

.channel-card.wide {
  grid-column: span 8;
  grid-row: span 1;
}

.channel-card.tall {
  grid-column: span 4;
  grid-row: span 2;
}

.channel-card.highlighted {
  border: 2px solid #ff0000;
  box-shadow: 0 8px 32px rgba(255, 0, 0, 0.15);
}

/* Hover Effects */
.channel-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.channel-card.large:hover {
  box-shadow: 0 16px 48px rgba(255, 107, 107, 0.3);
}

/* Animation */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Channel Header */
.channel-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.youtube-icon {
  color: #ff0000;
  font-size: 28px;
  margin-right: 12px;
  filter: drop-shadow(0 2px 4px rgba(255, 0, 0, 0.3));
}

.channel-card.large .youtube-icon {
  color: rgba(255, 255, 255, 0.9);
  font-size: 32px;
}

.channel-header h2 {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
  flex: 1;
}

.channel-card.large .channel-header h2 {
  color: white;
  font-size: 24px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Standard Channel Stats */
.channel-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.channel-card.wide .channel-stats {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.stat {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  font-size: 20px;
  margin-right: 12px;
  color: #666;
  min-width: 24px;
}

.channel-card.large .stat-icon {
  color: rgba(255, 255, 255, 0.8);
}

.stat-details {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-weight: 700;
  color: #333;
  font-size: 16px;
  line-height: 1.2;
}

.channel-card.large .stat-value {
  color: white;
}

.stat-label {
  font-size: 13px;
  color: #777;
  margin-top: 2px;
}

.channel-card.large .stat-label {
  color: rgba(255, 255, 255, 0.8);
}

/* Large Card Specific Layouts */
.large-card-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 20px;
}

.primary-stat {
  display: flex;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.primary-stat-icon {
  font-size: 36px;
  margin-right: 16px;
  color: rgba(255, 255, 255, 0.9);
}

.primary-stat-details {
  display: flex;
  flex-direction: column;
}

.primary-stat-value {
  font-weight: 800;
  color: white;
  font-size: 28px;
  line-height: 1;
}

.primary-stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.secondary-stats {
  display: flex;
  gap: 16px;
}

.secondary-stat {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

/* Last Updated */
.last-updated {
  font-size: 11px;
  color: #999;
  text-align: right;
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.channel-card.large .last-updated {
  color: rgba(255, 255, 255, 0.7);
  border-top-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .asymmetric-grid {
    grid-template-columns: repeat(8, 1fr);
  }

  .channel-card {
    grid-column: span 4;
  }

  .channel-card.large {
    grid-column: span 8;
    grid-row: span 1;
  }

  .channel-card.wide {
    grid-column: span 8;
  }

  .channel-card.tall {
    grid-column: span 4;
    grid-row: span 1;
  }
}

@media (max-width: 768px) {
  .asymmetric-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }

  .channel-card {
    grid-column: span 4;
    grid-row: span 1;
    padding: 20px;
  }

  .channel-card.large,
  .channel-card.wide,
  .channel-card.tall {
    grid-column: span 4;
    grid-row: span 1;
  }

  .channel-stats {
    flex-direction: column;
    gap: 8px;
  }

  .large-card-content {
    gap: 16px;
  }

  .primary-stat {
    padding: 16px;
  }

  .primary-stat-value {
    font-size: 24px;
  }

  .secondary-stats {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .asymmetric-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .channel-card {
    grid-column: span 1;
    padding: 16px;
  }

  .channel-header h2 {
    font-size: 18px;
  }

  .channel-card.large .channel-header h2 {
    font-size: 20px;
  }

  .primary-stat-value {
    font-size: 20px;
  }

  .stat-value {
    font-size: 14px;
  }
}

/* Enhanced Visual Effects */
.channel-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff0000, #ff6b6b, #ff0000);
  border-radius: 12px 12px 0 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.channel-card:hover::before {
  opacity: 1;
}

.channel-card.large::before {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.3));
  opacity: 1;
}

/* Staggered Animation for Grid Items */
.channel-card:nth-child(1) { --animation-delay: 0.1s; }
.channel-card:nth-child(2) { --animation-delay: 0.2s; }
.channel-card:nth-child(3) { --animation-delay: 0.3s; }
.channel-card:nth-child(4) { --animation-delay: 0.4s; }
.channel-card:nth-child(5) { --animation-delay: 0.5s; }
.channel-card:nth-child(6) { --animation-delay: 0.6s; }
.channel-card:nth-child(7) { --animation-delay: 0.7s; }
.channel-card:nth-child(8) { --animation-delay: 0.8s; }
.channel-card:nth-child(n+9) { --animation-delay: 0.9s; }

