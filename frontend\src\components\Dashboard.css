/* VELIO Dashboard Styles */
.dashboard-container {
  padding: var(--spacing-xl) var(--spacing-2xl);
  max-width: 1200px;
  min-height: 100vh;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.test-label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.menu-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.menu-button:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

/* Empty State */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.create-channel-btn {
  display: inline-block;
  padding: var(--spacing-lg) var(--spacing-xl);
  border: 2px dashed var(--accent-primary);
  border-radius: var(--radius-md);
  color: var(--accent-primary);
  text-decoration: none;
  font-size: var(--font-size-base);
  font-weight: 500;
  transition: all 0.2s ease;
  background-color: transparent;
}

.create-channel-btn:hover {
  background-color: rgba(0, 255, 136, 0.1);
  border-style: solid;
}

/* Channel Grid (when channels exist) */
.asymmetric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.channel-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.channel-card:hover {
  background-color: var(--hover-bg);
  border-color: var(--accent-primary);
}

.channel-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.channel-header h3 {
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
}

.channel-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.stat-value {
  color: var(--text-primary);
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--spacing-lg);
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .asymmetric-grid {
    grid-template-columns: 1fr;
  }
}
