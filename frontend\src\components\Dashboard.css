.dashboard-container {
  padding: 20px 0;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  font-size: 28px;
  color: #333;
  font-weight: 600;
}

.controls {
  display: flex;
  align-items: center;
}

.sort-control {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.sort-control label {
  margin-right: 10px;
  font-weight: 500;
}

.sort-control select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
}

.add-channel-btn {
  display: inline-block;
  background-color: #ff0000;
  color: white;
  padding: 10px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.add-channel-btn:hover {
  background-color: #d32f2f;
}

.no-channels {
  background-color: #fff;
  padding: 40px;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-channels p {
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
}

.channels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 20px;
}

.channel-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.2s, box-shadow 0.2s;
  text-decoration: none;
  color: inherit;
}

.channel-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.channel-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.youtube-icon {
  color: #ff0000;
  font-size: 24px;
  margin-right: 12px;
}

.channel-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.channel-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #666;
}

.stat-details {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #777;
}

.last-updated {
  font-size: 12px;
  color: #999;
  text-align: right;
}

