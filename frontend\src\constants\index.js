/**
 * Application constants and configuration
 */

// API Configuration
export const API_CONFIG = {
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Sort Options for Channels
export const SORT_OPTIONS = [
  { value: 'subscriber_count', label: 'Subscribers' },
  { value: 'view_count', label: 'Views' },
  { value: 'video_count', label: 'Videos' },
  { value: 'title', label: 'Name' },
  { value: 'last_updated', label: 'Last Updated' },
];

// Grid Layout Configuration
export const GRID_CONFIG = {
  COLUMNS: {
    DESKTOP: 12,
    TABLET: 8,
    MOBILE: 4,
    SMALL_MOBILE: 1,
  },
  BREAKPOINTS: {
    DESKTOP: 1200,
    TABLET: 768,
    MOBILE: 480,
  },
  CARD_SIZES: {
    LARGE: { columns: 6, rows: 2 },
    WIDE: { columns: 8, rows: 1 },
    TALL: { columns: 4, rows: 2 },
    STANDARD: { columns: 4, rows: 1 },
  },
};

// Animation Configuration
export const ANIMATION_CONFIG = {
  DURATION: {
    FAST: 200,
    NORMAL: 300,
    SLOW: 500,
  },
  EASING: {
    EASE_OUT: 'cubic-bezier(0.4, 0, 0.2, 1)',
    EASE_IN_OUT: 'cubic-bezier(0.4, 0, 0.6, 1)',
    BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  STAGGER_DELAY: 0.1, // seconds
};

// Color Scheme
export const COLORS = {
  PRIMARY: '#ff0000',
  PRIMARY_LIGHT: '#ff6b6b',
  PRIMARY_DARK: '#d32f2f',
  SECONDARY: '#666666',
  SUCCESS: '#4caf50',
  WARNING: '#ff9800',
  ERROR: '#f44336',
  INFO: '#2196f3',
  BACKGROUND: '#ffffff',
  SURFACE: '#f5f5f5',
  TEXT_PRIMARY: '#333333',
  TEXT_SECONDARY: '#777777',
  TEXT_DISABLED: '#999999',
  BORDER: '#e0e0e0',
  SHADOW: 'rgba(0, 0, 0, 0.1)',
};

// Typography
export const TYPOGRAPHY = {
  FONT_FAMILY: {
    PRIMARY: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
    MONOSPACE: 'source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace',
  },
  FONT_SIZES: {
    XS: '0.75rem',
    SM: '0.875rem',
    BASE: '1rem',
    LG: '1.125rem',
    XL: '1.25rem',
    '2XL': '1.5rem',
    '3XL': '1.875rem',
    '4XL': '2.25rem',
  },
  FONT_WEIGHTS: {
    LIGHT: 300,
    NORMAL: 400,
    MEDIUM: 500,
    SEMIBOLD: 600,
    BOLD: 700,
    EXTRABOLD: 800,
  },
};

// Spacing Scale
export const SPACING = {
  XS: '0.25rem',
  SM: '0.5rem',
  BASE: '1rem',
  LG: '1.5rem',
  XL: '2rem',
  '2XL': '3rem',
  '3XL': '4rem',
  '4XL': '6rem',
};

// Z-Index Scale
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  CHANNEL_NOT_FOUND: 'Channel not found. Please check the URL and try again.',
  CHANNEL_ALREADY_EXISTS: 'This channel is already being tracked.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  CHANNEL_ADDED: 'Channel added successfully!',
  CHANNEL_DELETED: 'Channel deleted successfully!',
  CHANNEL_UPDATED: 'Channel updated successfully!',
  DATA_REFRESHED: 'Data refreshed successfully!',
};

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'youtube-stats-theme',
  SORT_PREFERENCE: 'youtube-stats-sort',
  VIEW_PREFERENCE: 'youtube-stats-view',
};

// Regular Expressions
export const REGEX = {
  YOUTUBE_URL: /^(https?:\/\/)?(www\.)?(youtube\.com\/(channel\/|c\/|user\/)|youtu\.be\/)/,
  YOUTUBE_CHANNEL_ID: /^UC[a-zA-Z0-9_-]{22}$/,
};

// Feature Flags
export const FEATURES = {
  DARK_MODE: false,
  ANALYTICS: true,
  NOTIFICATIONS: true,
  EXPORT: false,
};

export default {
  API_CONFIG,
  SORT_OPTIONS,
  GRID_CONFIG,
  ANIMATION_CONFIG,
  COLORS,
  TYPOGRAPHY,
  SPACING,
  Z_INDEX,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  STORAGE_KEYS,
  REGEX,
  FEATURES,
};
