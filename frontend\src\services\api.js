import axios from 'axios';
import { API_CONFIG, ERROR_MESSAGES } from '../constants';

/**
 * API Service Layer
 * Centralized API communication with error handling and retry logic
 */

// Create axios instance with default configuration
const createApiInstance = () => {
  const baseURL = window._env_?.REACT_APP_API_URL || 
                  process.env.REACT_APP_API_URL || 
                  'http://localhost:8000';

  const instance = axios.create({
    baseURL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config) => {
      // Add timestamp to prevent caching
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      // Enhanced error handling
      if (error.response) {
        // Server responded with error status
        const { status, data } = error.response;
        
        switch (status) {
          case 400:
            error.message = data?.detail || ERROR_MESSAGES.VALIDATION_ERROR;
            break;
          case 404:
            error.message = data?.detail || ERROR_MESSAGES.CHANNEL_NOT_FOUND;
            break;
          case 409:
            error.message = data?.detail || ERROR_MESSAGES.CHANNEL_ALREADY_EXISTS;
            break;
          case 500:
            error.message = ERROR_MESSAGES.SERVER_ERROR;
            break;
          default:
            error.message = data?.detail || ERROR_MESSAGES.GENERIC_ERROR;
        }
      } else if (error.request) {
        // Network error
        error.message = ERROR_MESSAGES.NETWORK_ERROR;
      }
      
      return Promise.reject(error);
    }
  );

  return instance;
};

// Create API instance
const api = createApiInstance();

/**
 * Retry wrapper for API calls
 */
const withRetry = async (apiCall, retries = API_CONFIG.RETRY_ATTEMPTS) => {
  try {
    return await apiCall();
  } catch (error) {
    if (retries > 0 && error.code !== 'ECONNABORTED') {
      await new Promise(resolve => setTimeout(resolve, API_CONFIG.RETRY_DELAY));
      return withRetry(apiCall, retries - 1);
    }
    throw error;
  }
};

/**
 * Channel API endpoints
 */
export const channelApi = {
  /**
   * Get all channels with optional sorting
   */
  getAll: (sortBy = 'subscriber_count') => 
    withRetry(() => api.get('/channels/', { params: { sort_by: sortBy } })),

  /**
   * Get a specific channel by ID
   */
  getById: (channelId) => 
    withRetry(() => api.get(`/channels/${channelId}`)),

  /**
   * Add a new channel
   */
  create: (channelData) => 
    withRetry(() => api.post('/channels/', channelData)),

  /**
   * Update channel data
   */
  update: (channelId, channelData) => 
    withRetry(() => api.put(`/channels/${channelId}`, channelData)),

  /**
   * Delete a channel
   */
  delete: (channelId) => 
    withRetry(() => api.delete(`/channels/${channelId}`)),

  /**
   * Refresh channel data
   */
  refresh: (channelId) => 
    withRetry(() => api.post(`/channels/${channelId}/refresh`)),

  /**
   * Get channel statistics history
   */
  getHistory: (channelId, days = 30) => 
    withRetry(() => api.get(`/channels/${channelId}/history`, { 
      params: { days } 
    })),
};

/**
 * Health check endpoint
 */
export const healthApi = {
  check: () => withRetry(() => api.get('/health')),
};

/**
 * Generic API utilities
 */
export const apiUtils = {
  /**
   * Check if API is available
   */
  isAvailable: async () => {
    try {
      await healthApi.check();
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Get API base URL
   */
  getBaseUrl: () => api.defaults.baseURL,

  /**
   * Update API base URL
   */
  setBaseUrl: (url) => {
    api.defaults.baseURL = url;
  },
};

// Export the configured axios instance for direct use if needed
export { api };

// Default export
export default {
  channelApi,
  healthApi,
  apiUtils,
  api,
};
