import React from 'react';
import { FaYoutube } from 'react-icons/fa';
import './Loading.css';

/**
 * Loading Component
 * Modern loading spinner with YouTube branding
 */
const Loading = ({ 
  message = 'Loading YouTube statistics...', 
  size = 'medium',
  fullScreen = false 
}) => {
  const sizeClass = `loading-${size}`;
  const containerClass = fullScreen ? 'loading-container fullscreen' : 'loading-container';

  return (
    <div className={containerClass}>
      <div className={`loading-content ${sizeClass}`}>
        {/* Animated YouTube icon */}
        <div className="loading-icon">
          <FaYoutube />
        </div>
        
        {/* Spinning ring */}
        <div className="loading-spinner">
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
        </div>
        
        {/* Loading message */}
        <div className="loading-message">
          {message}
        </div>
        
        {/* Animated dots */}
        <div className="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>
  );
};

/**
 * Skeleton Loading Component
 * For content placeholders
 */
export const SkeletonLoader = ({ 
  lines = 3, 
  height = '20px', 
  className = '' 
}) => {
  return (
    <div className={`skeleton-container ${className}`}>
      {Array.from({ length: lines }, (_, index) => (
        <div 
          key={index}
          className="skeleton-line"
          style={{ 
            height,
            width: index === lines - 1 ? '70%' : '100%'
          }}
        />
      ))}
    </div>
  );
};

/**
 * Card Skeleton for Dashboard
 */
export const CardSkeleton = ({ className = '' }) => {
  return (
    <div className={`card-skeleton ${className}`}>
      <div className="skeleton-header">
        <div className="skeleton-avatar"></div>
        <div className="skeleton-title">
          <SkeletonLoader lines={2} height="16px" />
        </div>
      </div>
      
      <div className="skeleton-stats">
        <div className="skeleton-stat">
          <SkeletonLoader lines={1} height="24px" />
          <SkeletonLoader lines={1} height="14px" />
        </div>
        <div className="skeleton-stat">
          <SkeletonLoader lines={1} height="24px" />
          <SkeletonLoader lines={1} height="14px" />
        </div>
        <div className="skeleton-stat">
          <SkeletonLoader lines={1} height="24px" />
          <SkeletonLoader lines={1} height="14px" />
        </div>
      </div>
    </div>
  );
};

/**
 * Button Loading Component
 */
export const ButtonLoading = ({ 
  children, 
  loading = false, 
  disabled = false,
  ...props 
}) => {
  return (
    <button 
      {...props}
      disabled={disabled || loading}
      className={`${props.className || ''} ${loading ? 'loading' : ''}`}
    >
      {loading && (
        <span className="button-spinner">
          <div className="spinner-small"></div>
        </span>
      )}
      <span className={loading ? 'button-text-loading' : ''}>
        {children}
      </span>
    </button>
  );
};

export default Loading;
