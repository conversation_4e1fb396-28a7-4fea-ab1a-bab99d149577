import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FaYoutube, FaSort, FaEye, FaVideo, FaUserFriends } from 'react-icons/fa';
import './Dashboard.css';

const Dashboard = ({ apiBaseUrl }) => {
  const [channels, setChannels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortBy, setSortBy] = useState('subscriber_count');

  useEffect(() => {
    fetchChannels();
  }, [sortBy]);

  const fetchChannels = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${apiBaseUrl}/channels/?sort_by=${sortBy}`);
      setChannels(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching channels:', err);
      setError('Failed to load YouTube channels. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleSortChange = (e) => {
    setSortBy(e.target.value);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num);
  };

  if (loading) {
    return <div className="loading">Loading YouTube statistics...</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>YouTube Channel Statistics</h1>
        <div className="controls">
          <div className="sort-control">
            <label htmlFor="sort-select">Sort by:</label>
            <select id="sort-select" value={sortBy} onChange={handleSortChange}>
              <option value="subscriber_count">Subscribers</option>
              <option value="view_count">Views</option>
              <option value="video_count">Videos</option>
            </select>
          </div>
          <Link to="/add-channel" className="add-channel-btn">
            Add Channel
          </Link>
        </div>
      </div>

      {channels.length === 0 ? (
        <div className="no-channels">
          <p>No YouTube channels added yet. Add a channel to start tracking.</p>
        </div>
      ) : (
        <div className="asymmetric-grid">
          {channels.map((channel, index) => {
            // Determine card size and layout based on index and metrics
            const isLarge = index === 0 || (index % 5 === 0 && index > 0);
            const isWide = index % 3 === 1 && !isLarge;
            const isTall = index % 4 === 2 && !isLarge && !isWide;
            const isHighlighted = channel.subscriber_count > 1000000 || index === 0;

            const cardClass = `channel-card ${isLarge ? 'large' : ''} ${isWide ? 'wide' : ''} ${isTall ? 'tall' : ''} ${isHighlighted ? 'highlighted' : ''}`;

            return (
              <Link
                to={`/channels/${channel.channel_id}`}
                className={cardClass}
                key={channel.channel_id}
                style={{
                  '--animation-delay': `${index * 0.1}s`
                }}
              >
                <div className="channel-header">
                  <FaYoutube className="youtube-icon" />
                  <h2>{channel.title}</h2>
                </div>

                {isLarge ? (
                  // Large card layout with prominent stats
                  <div className="large-card-content">
                    <div className="primary-stat">
                      <FaUserFriends className="primary-stat-icon" />
                      <div className="primary-stat-details">
                        <span className="primary-stat-value">{formatNumber(channel.subscriber_count)}</span>
                        <span className="primary-stat-label">Subscribers</span>
                      </div>
                    </div>
                    <div className="secondary-stats">
                      <div className="secondary-stat">
                        <FaVideo className="stat-icon" />
                        <div className="stat-details">
                          <span className="stat-value">{formatNumber(channel.video_count)}</span>
                          <span className="stat-label">Videos</span>
                        </div>
                      </div>
                      <div className="secondary-stat">
                        <FaEye className="stat-icon" />
                        <div className="stat-details">
                          <span className="stat-value">{formatNumber(channel.view_count)}</span>
                          <span className="stat-label">Views</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Standard card layout
                  <div className="channel-stats">
                    <div className="stat">
                      <FaUserFriends className="stat-icon" />
                      <div className="stat-details">
                        <span className="stat-value">{formatNumber(channel.subscriber_count)}</span>
                        <span className="stat-label">Subscribers</span>
                      </div>
                    </div>
                    <div className="stat">
                      <FaVideo className="stat-icon" />
                      <div className="stat-details">
                        <span className="stat-value">{formatNumber(channel.video_count)}</span>
                        <span className="stat-label">Videos</span>
                      </div>
                    </div>
                    <div className="stat">
                      <FaEye className="stat-icon" />
                      <div className="stat-details">
                        <span className="stat-value">{formatNumber(channel.view_count)}</span>
                        <span className="stat-label">Views</span>
                      </div>
                    </div>
                  </div>
                )}

                <div className="last-updated">
                  Last updated: {new Date(channel.last_updated).toLocaleString()}
                </div>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default Dashboard;
