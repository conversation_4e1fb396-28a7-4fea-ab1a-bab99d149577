import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { FaYoutube, FaSort, FaEye, FaVideo, FaUserFriends } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useChannels } from '../hooks/useChannels';
import { formatNumber } from '../utils/formatters';
import { SORT_OPTIONS } from '../constants';
import './Dashboard.css';

const Dashboard = () => {
  const [sortBy, setSortBy] = useState('subscriber_count');
  const { channels, loading, error, fetchChannels } = useChannels();

  // Fetch channels when sort option changes
  useEffect(() => {
    fetchChannels(sortBy);
  }, [sortBy, fetchChannels]);

  // Show error toast when error occurs
  useEffect(() => {
    if (error) {
      toast.error(`Error: ${error}`);
    }
  }, [error]);

  // Handle sort change with useCallback for performance
  const handleSortChange = useCallback((event) => {
    setSortBy(event.target.value);
  }, []);

  // Memoized sort options for performance
  const sortOptions = useMemo(() => SORT_OPTIONS, []);

  if (loading) {
    return <div className="loading">Loading YouTube statistics...</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1 className="page-title">Tracked channels</h1>
        <div className="header-actions">
          <span className="test-label">test</span>
          <button className="menu-button">⋯</button>
        </div>
      </div>

      {channels.length === 0 ? (
        <div className="empty-state">
          <Link to="/add-channel" className="create-channel-btn">
            + Create new channel list
          </Link>
        </div>
      ) : (
        <div className="asymmetric-grid">
          {channels.map((channel, index) => {
            // Determine card size and layout based on index and metrics
            const isLarge = index === 0 || (index % 5 === 0 && index > 0);
            const isWide = index % 3 === 1 && !isLarge;
            const isTall = index % 4 === 2 && !isLarge && !isWide;
            const isHighlighted = channel.subscriber_count > 1000000 || index === 0;

            const cardClass = `channel-card ${isLarge ? 'large' : ''} ${isWide ? 'wide' : ''} ${isTall ? 'tall' : ''} ${isHighlighted ? 'highlighted' : ''}`;

            return (
              <Link
                to={`/channels/${channel.channel_id}`}
                className={cardClass}
                key={channel.channel_id}
                style={{
                  '--animation-delay': `${index * 0.1}s`
                }}
              >
                <div className="channel-header">
                  <FaYoutube className="youtube-icon" />
                  <h2>{channel.title}</h2>
                </div>

                {isLarge ? (
                  // Large card layout with prominent stats
                  <div className="large-card-content">
                    <div className="primary-stat">
                      <FaUserFriends className="primary-stat-icon" />
                      <div className="primary-stat-details">
                        <span className="primary-stat-value">{formatNumber(channel.subscriber_count)}</span>
                        <span className="primary-stat-label">Subscribers</span>
                      </div>
                    </div>
                    <div className="secondary-stats">
                      <div className="secondary-stat">
                        <FaVideo className="stat-icon" />
                        <div className="stat-details">
                          <span className="stat-value">{formatNumber(channel.video_count)}</span>
                          <span className="stat-label">Videos</span>
                        </div>
                      </div>
                      <div className="secondary-stat">
                        <FaEye className="stat-icon" />
                        <div className="stat-details">
                          <span className="stat-value">{formatNumber(channel.view_count)}</span>
                          <span className="stat-label">Views</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Standard card layout
                  <div className="channel-stats">
                    <div className="stat">
                      <FaUserFriends className="stat-icon" />
                      <div className="stat-details">
                        <span className="stat-value">{formatNumber(channel.subscriber_count)}</span>
                        <span className="stat-label">Subscribers</span>
                      </div>
                    </div>
                    <div className="stat">
                      <FaVideo className="stat-icon" />
                      <div className="stat-details">
                        <span className="stat-value">{formatNumber(channel.video_count)}</span>
                        <span className="stat-label">Videos</span>
                      </div>
                    </div>
                    <div className="stat">
                      <FaEye className="stat-icon" />
                      <div className="stat-details">
                        <span className="stat-value">{formatNumber(channel.view_count)}</span>
                        <span className="stat-label">Views</span>
                      </div>
                    </div>
                  </div>
                )}

                <div className="last-updated">
                  Last updated: {new Date(channel.last_updated).toLocaleString()}
                </div>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default Dashboard;
