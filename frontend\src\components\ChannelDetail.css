.channel-detail-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-bottom: 20px;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.channel-title {
  display: flex;
  align-items: center;
}

.channel-title h1 {
  font-size: 24px;
  font-weight: 600;
  margin-left: 12px;
}

.channel-actions {
  display: flex;
  align-items: center;
}

.delete-channel-btn {
  display: flex;
  align-items: center;
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.delete-channel-btn:hover {
  background-color: #d32f2f;
}

.delete-confirmation {
  display: flex;
  align-items: center;
}

.delete-confirmation span {
  margin-right: 12px;
  font-weight: 500;
}

.confirm-delete-btn, .cancel-delete-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-left: 8px;
}

.confirm-delete-btn {
  background-color: #f44336;
  color: white;
}

.cancel-delete-btn {
  background-color: #e0e0e0;
  color: #333;
}

.channel-description {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.channel-description p {
  margin-bottom: 16px;
  line-height: 1.6;
  color: #555;
}

.channel-metadata {
  display: flex;
  flex-wrap: wrap;
  font-size: 13px;
  color: #777;
}

.channel-metadata span {
  margin-right: 20px;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 36px;
  margin-right: 20px;
}

.stat-icon.subscribers {
  color: #4caf50;
}

.stat-icon.videos {
  color: #2196f3;
}

.stat-icon.views {
  color: #ff9800;
}

.stat-content h3 {
  font-size: 16px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.history-section {
  margin-top: 30px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.history-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.metric-selector {
  display: flex;
  align-items: center;
}

.metric-selector label {
  margin-right: 10px;
  font-weight: 500;
}

.metric-selector select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
}

.chart-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 400px;
}

.no-history {
  background-color: #f8f9fa;
  padding: 30px;
  text-align: center;
  border-radius: 8px;
}

.no-history p {
  color: #666;
  font-size: 16px;
}

