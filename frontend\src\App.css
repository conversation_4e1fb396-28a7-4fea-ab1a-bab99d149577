/* Modern App.css with CSS Custom Properties */

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Root CSS Custom Properties */
:root {
  /* Colors - Already defined in Dashboard.css, but ensuring consistency */
  --color-primary: #ff0000;
  --color-primary-light: #ff6b6b;
  --color-primary-dark: #d32f2f;
  --color-text-primary: #333;
  --color-text-secondary: #666;
  --color-text-muted: #777;
  --color-border: #f5f5f5;
  --color-background: #ffffff;
  --color-surface: #f8f9fa;
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-hover: rgba(0, 0, 0, 0.15);

  /* Typography */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --font-family-mono: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Base HTML and Body */
html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App Container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

/* Main Content Container */
.content-container {
  flex: 1;
  padding: var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  container-type: inline-size;
}

/* 404 Not Found Page */
.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: var(--spacing-2xl);
}

.not-found h1 {
  font-size: 4rem;
  font-weight: 800;
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.not-found p {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
}

/* Custom Toast Styles */
.custom-toast {
  border-radius: var(--radius-lg);
  box-shadow: 0 8px 32px var(--color-shadow);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.custom-toast-body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.custom-toast-progress {
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
}

/* Loading States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.error-message {
  background-color: rgba(255, 0, 0, 0.05);
  color: var(--color-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) 0;
  font-weight: var(--font-weight-medium);
  border: 1px solid rgba(255, 0, 0, 0.1);
  text-align: center;
}

/* Responsive Design */
@container (max-width: 768px) {
  .content-container {
    padding: var(--spacing-lg);
  }

  .not-found h1 {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: var(--spacing-md);
  }

  .not-found {
    padding: var(--spacing-xl);
  }

  .not-found h1 {
    font-size: 2.5rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for better accessibility */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-text-primary: #000000;
    --color-text-secondary: #333333;
    --color-border: #000000;
    --color-shadow: rgba(0, 0, 0, 0.5);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-text-primary: #ffffff;
    --color-text-secondary: #cccccc;
    --color-text-muted: #999999;
    --color-background: #1a1a1a;
    --color-surface: #2d2d2d;
    --color-border: #404040;
    --color-shadow: rgba(0, 0, 0, 0.3);
    --color-shadow-hover: rgba(0, 0, 0, 0.5);
  }

  body {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
  }

  .custom-toast {
    background-color: var(--color-background);
    color: var(--color-text-primary);
    border-color: var(--color-border);
  }
}

/* Print styles */
@media print {
  .app-container {
    background: white !important;
    color: black !important;
  }

  .custom-toast,
  .loading,
  .error-boundary {
    display: none !important;
  }

  .content-container {
    max-width: none !important;
    padding: 0 !important;
  }
}
