/* VELIO App Layout */

/* App Container - Sidebar Layout */
.app-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-primary);
}

/* Main Content Container */
.content-container {
  flex: 1;
  margin-left: 240px; /* Sidebar width */
  padding: var(--spacing-xl);
  background-color: var(--bg-primary);
  min-height: 100vh;
  transition: margin-left 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-container {
    margin-left: 0;
    padding: var(--spacing-lg);
  }
}

/* 404 Not Found Page */
.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-primary);
}

.not-found h1 {
  font-size: 4rem;
  font-weight: 800;
  color: var(--accent-primary);
  margin-bottom: var(--spacing-lg);
}

.not-found p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

/* Custom Toast Styles */
.custom-toast {
  border-radius: var(--radius-lg);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.custom-toast-body {
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
}

.custom-toast-progress {
  background: var(--accent-primary);
}

/* Loading States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.error-message {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff6b6b;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) 0;
  border: 1px solid rgba(255, 0, 0, 0.2);
  text-align: center;
}

/* Responsive Design */
@media (max-width: 480px) {
  .not-found h1 {
    font-size: 2.5rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}
